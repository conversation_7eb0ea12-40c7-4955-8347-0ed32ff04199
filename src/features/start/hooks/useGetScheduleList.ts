import { useQuery } from "@apollo/client";

import { GET_SCHEDULE_LIST } from "@/apis/gql/operations/schedule";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type {
  Schedule,
  GetScheduleListInput,
} from "@/apis/gql/generated/types";

export const useGetScheduleList = (
  input: GetScheduleListInput,
  skip: boolean,
  onSuccess: (schedules: Schedule[]) => void,
) => {
  const { handleError } = useErrorHandler();

  const { data, refetch } = useQuery<{
    getScheduleList: Schedule[];
  }>(GET_SCHEDULE_LIST, {
    variables: {
      input,
    },
    skip,
    onCompleted: (data) => {
      if (data) {
        onSuccess(data.getScheduleList);
      }
    },
    onError: (error) => {
      handleError({ error });
    },
    fetchPolicy: "no-cache",
  });

  return {
    schedules: data?.getScheduleList,
    refetch,
  };
};
